<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碰撞测试数据异常检测系统 - POC演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .main-content {
            padding: 20px;
            display: flex;
            gap: 20px;
            min-height: calc(100vh - 200px);
        }

        .left-panel {
            flex: 1;
            min-width: 400px;
        }

        .right-panel {
            flex: 1.5;
            min-width: 600px;
        }

        .upload-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            border: 2px dashed #dee2e6;
            transition: all 0.3s ease;
            height: fit-content;
        }

        .upload-section:hover {
            border-color: #007bff;
            background: #f0f8ff;
        }

        .upload-area {
            text-align: center;
            padding: 20px;
        }

        .upload-icon {
            font-size: 4em;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,123,255,0.3);
        }

        .detect-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
            display: none;
        }

        .detect-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40,167,69,0.3);
        }

        .detect-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
            display: none;
        }

        .results-section {
            display: none;
            height: fit-content;
        }

        .results-header {
            background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
            color: white;
            padding: 20px;
            border-radius: 10px 10px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .results-title {
            font-size: 1.5em;
            margin: 0;
        }

        .file-name-display {
            font-size: 0.9em;
            opacity: 0.9;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
        }

        .results-content {
            background: white;
            border: 1px solid #dee2e6;
            border-radius: 0 0 10px 10px;
            padding: 30px;
        }

        .file-metadata {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #17a2b8;
        }

        .metadata-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .metadata-title {
            font-weight: bold;
            color: #2c3e50;
        }

        .toggle-btn {
            background: none;
            border: none;
            color: #17a2b8;
            cursor: pointer;
            font-size: 0.9em;
            padding: 2px 8px;
            border-radius: 3px;
            transition: background 0.3s;
        }

        .toggle-btn:hover {
            background: rgba(23, 162, 184, 0.1);
        }

        .metadata-content {
            display: none;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            max-height: 200px;
            overflow-y: auto;
        }

        .metadata-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            font-size: 0.9em;
        }

        .metadata-item {
            display: flex;
            justify-content: space-between;
        }

        .metadata-label {
            font-weight: 500;
            color: #6c757d;
        }

        .metadata-value {
            color: #2c3e50;
            font-weight: bold;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #007bff;
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
            margin-bottom: 5px;
        }

        .metric-label {
            color: #6c757d;
            font-size: 0.9em;
        }

        .visualization-container {
            text-align: center;
            margin: 30px 0;
        }

        .visualization-container img {
            max-width: 100%;
            height: auto;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #f5c6cb;
            display: none;
        }

        .success-message {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border: 1px solid #c3e6cb;
            display: none;
        }

        .footer {
            background: #2c3e50;
            color: white;
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
        }

        .status-offline {
            background: #dc3545;
        }

        .anomaly-high {
            border-left-color: #dc3545 !important;
        }

        .anomaly-medium {
            border-left-color: #ffc107 !important;
        }

        .anomaly-low {
            border-left-color: #28a745 !important;
        }

        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }

            .left-panel, .right-panel {
                min-width: auto;
            }
        }

        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }

            .left-panel, .right-panel {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>碰撞测试数据异常检测系统</h1>
            <p>基于高级LSTM自编码器的智能异常检测技术验证POC</p>
            <p><span class="status-indicator status-offline" id="statusIndicator"></span><span id="statusText">连接中...</span></p>
        </div>

        <div class="main-content">
            <!-- 左侧面板 - 输入区域 -->
            <div class="left-panel">
                <!-- 文件上传区域 -->
                <div class="upload-section">
                    <div class="upload-area">
                        <div class="upload-icon">📁</div>
                        <h3>上传碰撞测试数据文件</h3>
                        <p>支持格式：CSV、TXT、.001 文件</p>
                        <input type="file" id="fileInput" class="file-input" accept=".csv,.txt,.001">
                        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                            选择文件
                        </button>
                    </div>

                    <div class="file-info" id="fileInfo">
                        <h4>已选择文件：</h4>
                        <p id="fileName"></p>
                        <p id="fileSize"></p>
                        <button class="detect-btn" id="detectBtn" onclick="detectAnomalies()">
                            🔍 开始异常检测
                        </button>
                    </div>
                </div>

                <!-- 加载状态 -->
                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在进行异常检测分析，请稍候...</p>
                </div>

                <!-- 错误消息 -->
                <div class="error-message" id="errorMessage"></div>

                <!-- 成功消息 -->
                <div class="success-message" id="successMessage"></div>
            </div>

            <!-- 右侧面板 - 输出区域 -->
            <div class="right-panel">
                <!-- 检测结果区域 -->
                <div class="results-section" id="resultsSection">
                    <div class="results-header">
                        <h2 class="results-title">🎯 异常检测结果</h2>
                        <div class="file-name-display" id="fileNameDisplay" style="display: none;"></div>
                    </div>
                    <div class="results-content">
                        <!-- 文件元数据信息 -->
                        <div class="file-metadata" id="fileMetadata" style="display: none;">
                            <div class="metadata-header" onclick="toggleMetadata()">
                                <div class="metadata-title">📋 文件元数据信息</div>
                                <button class="toggle-btn" id="toggleBtn">展开</button>
                            </div>
                            <div class="metadata-summary" id="metadataSummary"></div>
                            <div class="metadata-content" id="metadataContent"></div>
                        </div>

                        <div class="metrics-grid">
                            <div class="metric-card" id="dataPointsCard">
                                <div class="metric-value" id="dataPoints">-</div>
                                <div class="metric-label">数据点数量</div>
                            </div>
                            <div class="metric-card" id="anomalyCountCard">
                                <div class="metric-value" id="anomalyCount">-</div>
                                <div class="metric-label">异常序列数</div>
                            </div>
                            <div class="metric-card" id="totalSequencesCard">
                                <div class="metric-value" id="totalSequences">-</div>
                                <div class="metric-label">总序列数</div>
                            </div>
                            <div class="metric-card" id="anomalyPercentageCard">
                                <div class="metric-value" id="anomalyPercentage">-</div>
                                <div class="metric-label">异常比例 (%)</div>
                            </div>
                        </div>

                        <div class="visualization-container">
                            <h3>📊 异常检测可视化结果</h3>
                            <img id="visualizationImage" src="" alt="异常检测可视化图表" style="display: none;">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 山东山创网络科技有限公司 | 技术验证：刁国亮 (<EMAIL>)</p>
            <p>中汽数据（天津）有限公司 - 碰撞测试数据异常AI智能检测技术验证POC项目</p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000';
        let selectedFile = null;

        // 页面加载时检查API服务状态
        window.onload = function() {
            checkApiStatus();
        };

        // 检查API服务状态
        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    document.getElementById('statusIndicator').className = 'status-indicator status-online';
                    document.getElementById('statusText').textContent = '服务在线';
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                document.getElementById('statusIndicator').className = 'status-indicator status-offline';
                document.getElementById('statusText').textContent = '服务离线';
                showError('无法连接到API服务，请确保服务已启动');
            }
        }

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = `文件大小: ${(file.size / 1024).toFixed(2)} KB`;
                document.getElementById('fileInfo').style.display = 'block';
                document.getElementById('detectBtn').style.display = 'inline-block';

                // 更新结果标题中的文件名
                document.getElementById('fileNameDisplay').textContent = file.name;
                document.getElementById('fileNameDisplay').style.display = 'block';

                hideMessages();
            }
        });

        // 异常检测函数
        async function detectAnomalies() {
            if (!selectedFile) {
                showError('请先选择文件');
                return;
            }

            // 记录开始时间
            window.startTime = new Date();

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('detectBtn').disabled = true;
            document.getElementById('resultsSection').style.display = 'none';
            hideMessages();

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch(`${API_BASE_URL}/detect`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    displayResults(result);
                    showSuccess('异常检测完成！');
                } else {
                    throw new Error(result.error || '检测失败');
                }
            } catch (error) {
                showError(`检测失败: ${error.message}`);
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('detectBtn').disabled = false;
            }
        }

        // 显示检测结果
        function displayResults(result) {
            // 更新指标卡片
            document.getElementById('dataPoints').textContent = result.data_points.toLocaleString();
            document.getElementById('anomalyCount').textContent = result.anomaly_count;
            document.getElementById('totalSequences').textContent = result.total_sequences;
            document.getElementById('anomalyPercentage').textContent = result.anomaly_percentage + '%';

            // 根据异常比例设置卡片颜色
            const anomalyCard = document.getElementById('anomalyPercentageCard');
            if (result.anomaly_percentage > 10) {
                anomalyCard.className = 'metric-card anomaly-high';
            } else if (result.anomaly_percentage > 5) {
                anomalyCard.className = 'metric-card anomaly-medium';
            } else {
                anomalyCard.className = 'metric-card anomaly-low';
            }

            // 显示文件元数据
            displayFileMetadata(result);

            // 显示可视化图表
            if (result.visualization) {
                const img = document.getElementById('visualizationImage');
                img.src = `data:image/png;base64,${result.visualization}`;
                img.style.display = 'block';
            }

            // 显示结果区域
            document.getElementById('resultsSection').style.display = 'block';
        }

        // 显示文件元数据
        function displayFileMetadata(result) {
            const metadataDiv = document.getElementById('fileMetadata');
            const summaryDiv = document.getElementById('metadataSummary');
            const contentDiv = document.getElementById('metadataContent');

            // 创建元数据摘要
            const summaryData = [
                { label: '文件名', value: result.filename },
                { label: '数据点数', value: result.data_points.toLocaleString() },
                { label: '文件大小', value: `${(selectedFile.size / 1024).toFixed(2)} KB` },
                { label: '处理时间', value: `${((new Date() - window.startTime) / 1000).toFixed(2)}秒` }
            ];

            summaryDiv.innerHTML = summaryData.map(item =>
                `<div class="metadata-item">
                    <span class="metadata-label">${item.label}:</span>
                    <span class="metadata-value">${item.value}</span>
                </div>`
            ).join('');

            // 创建详细元数据内容
            const detailData = {
                '文件信息': {
                    '原始文件名': result.filename,
                    '文件大小': `${selectedFile.size} bytes (${(selectedFile.size / 1024).toFixed(2)} KB)`,
                    '文件类型': selectedFile.type || '未知',
                    '最后修改': selectedFile.lastModified ? new Date(selectedFile.lastModified).toLocaleString() : '未知'
                },
                '检测参数': {
                    '序列长度': '100',
                    '滑动步长': '10',
                    '模型类型': '高级LSTM自编码器',
                    '检测阈值': '动态计算'
                },
                '处理结果': {
                    '数据点数量': result.data_points.toLocaleString(),
                    '序列总数': result.total_sequences,
                    '异常序列数': result.anomaly_count,
                    '异常比例': `${result.anomaly_percentage}%`,
                    '处理时间': `${((new Date() - window.startTime) / 1000).toFixed(2)}秒`
                }
            };

            let contentHtml = '';
            for (const [category, items] of Object.entries(detailData)) {
                contentHtml += `<div style="margin-bottom: 15px;">`;
                contentHtml += `<div style="font-weight: bold; color: #17a2b8; margin-bottom: 5px;">${category}:</div>`;
                for (const [key, value] of Object.entries(items)) {
                    contentHtml += `<div style="margin-left: 10px; margin-bottom: 2px;">`;
                    contentHtml += `<span style="color: #6c757d;">${key}:</span> `;
                    contentHtml += `<span style="color: #2c3e50; font-weight: 500;">${value}</span>`;
                    contentHtml += `</div>`;
                }
                contentHtml += `</div>`;
            }

            contentDiv.innerHTML = contentHtml;
            metadataDiv.style.display = 'block';
        }

        // 切换元数据显示
        function toggleMetadata() {
            const content = document.getElementById('metadataContent');
            const toggleBtn = document.getElementById('toggleBtn');

            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggleBtn.textContent = '收起';
            } else {
                content.style.display = 'none';
                toggleBtn.textContent = '展开';
            }
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 隐藏消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 定期检查服务状态
        setInterval(checkApiStatus, 30000); // 每30秒检查一次
    </script>
</body>
</html>
