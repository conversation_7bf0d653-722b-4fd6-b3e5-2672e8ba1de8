<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>碰撞测试数据异常检测系统 - POC演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary: #22d38b;
            --secondary: #3385ff;
            --neutrals-gray900: #151b1e;
            --neutrals-gray800: #1f292e;
            --neutrals-gray700: #324148;
            --neutrals-gray600: #4b626d;
            --neutrals-gray500: #5c7785;
            --neutrals-gray400: #7e95a0;
            --neutrals-gray300: #95a8b2;
            --neutrals-gray200: #becacf;
            --neutrals-gray100: #dce2e5;
            --neutrals-gray50: #eceff1;
            --dark: #18181e;
            --subtle: #424249;
            --text-primary: var(--neutrals-gray100);
            --text-secondary: var(--neutrals-gray200);
            --text-subtle: var(--neutrals-gray600);
            --border-radius: 0.75rem;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, var(--dark) 0%, var(--neutrals-gray900) 50%, var(--neutrals-gray800) 100%);
            min-height: 100vh;
            padding: 20px;
            color: var(--text-primary);
            line-height: 1.6;
        }

        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: var(--neutrals-gray900);
            border-radius: var(--border-radius);
            box-shadow: 0 25px 50px rgba(0,0,0,0.3), 0 0 0 1px var(--neutrals-gray800);
            overflow: hidden;
            border: 1px solid var(--neutrals-gray800);
        }

        .header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
            pointer-events: none;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 15px;
            font-weight: 800;
            text-shadow: 0 4px 8px rgba(0,0,0,0.2);
            letter-spacing: -0.02em;
            position: relative;
            z-index: 1;
        }

        .header p {
            font-size: 1.3em;
            opacity: 0.95;
            font-weight: 400;
            position: relative;
            z-index: 1;
        }

        .main-content {
            padding: 20px;
            display: flex;
            gap: 30px;
            min-height: calc(100vh - 200px);
        }

        .left-panel {
            flex: 1;
            min-width: 450px;
            background: var(--neutrals-gray800);
            border-radius: var(--border-radius);
            padding: 25px;
            border: 1px solid var(--neutrals-gray700);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }

        .right-panel {
            flex: 2;
            min-width: 800px;
            background: var(--neutrals-gray900);
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals-gray700);
            box-shadow: 0 15px 35px rgba(0,0,0,0.2);
            position: relative;
        }

        .upload-section {
            background: var(--neutrals-gray700);
            border-radius: var(--border-radius);
            padding: 30px;
            margin-bottom: 25px;
            border: 2px dashed var(--neutrals-gray600);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            height: fit-content;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .upload-section:hover {
            border-color: var(--primary);
            background: var(--neutrals-gray600);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(34, 211, 139, 0.15);
        }

        .upload-area {
            text-align: center;
            padding: 20px;
        }

        .upload-icon {
            font-size: 4em;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 16px 32px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1.1em;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 10px;
            box-shadow: 0 4px 15px rgba(34, 211, 139, 0.3);
            letter-spacing: 0.5px;
        }

        .upload-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(34, 211, 139, 0.4);
            background: linear-gradient(135deg, #1db584 0%, #2d7aff 100%);
        }

        .detect-btn {
            background: var(--primary);
            color: white;
            padding: 18px 40px;
            border: none;
            border-radius: var(--border-radius);
            font-size: 1.2em;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            margin: 20px 0;
            display: none;
            box-shadow: 0 6px 20px rgba(34, 211, 139, 0.3);
            letter-spacing: 0.5px;
        }

        .detect-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(34, 211, 139, 0.4);
            background: #1db584;
        }

        .detect-btn:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        .file-info {
            background: var(--neutrals-gray800);
            border-radius: var(--border-radius);
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid var(--primary);
            display: none;
            color: var(--text-primary);
        }

        .results-section {
            display: none;
            height: fit-content;
        }

        .results-header {
            background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
            color: white;
            padding: 20px;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .results-title {
            font-size: 1.5em;
            margin: 0;
        }

        .file-name-display {
            font-size: 0.9em;
            opacity: 0.9;
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
        }

        .tab-container {
            background: var(--neutrals-gray800);
            border: 1px solid var(--neutrals-gray700);
            border-radius: 0 0 var(--border-radius) var(--border-radius);
        }

        .tab-nav {
            display: flex;
            background: var(--neutrals-gray700);
            border-bottom: 1px solid var(--neutrals-gray600);
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 1em;
            color: var(--text-secondary);
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button.active {
            color: var(--primary);
            background: var(--neutrals-gray800);
            border-bottom-color: var(--primary);
            font-weight: 500;
        }

        .tab-button:hover {
            background: rgba(34, 211, 139, 0.1);
            color: var(--primary);
        }

        .tab-content {
            display: none;
            padding: 30px;
            color: var(--text-primary);
        }

        .tab-content.active {
            display: block;
        }

        .file-metadata {
            background: var(--neutrals-gray700);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid var(--secondary);
        }

        .metadata-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            margin-bottom: 10px;
        }

        .metadata-title {
            font-weight: bold;
            color: var(--text-primary);
        }

        .toggle-btn {
            background: none;
            border: none;
            color: var(--secondary);
            cursor: pointer;
            font-size: 0.9em;
            padding: 2px 8px;
            border-radius: var(--border-radius);
            transition: background 0.3s;
        }

        .toggle-btn:hover {
            background: rgba(51, 133, 255, 0.1);
        }

        .metadata-content {
            display: none;
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            background: var(--neutrals-gray700);
            color: var(--text-primary);
            padding: 10px;
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals-gray600);
            max-height: 200px;
            overflow-y: auto;
        }

        .metadata-summary {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            font-size: 0.9em;
        }

        .metadata-item {
            display: flex;
            justify-content: space-between;
        }

        .metadata-label {
            font-weight: 500;
            color: var(--text-secondary);
        }

        .metadata-value {
            color: var(--text-primary);
            font-weight: bold;
        }

        .channel-metadata {
            background: var(--neutrals-gray700);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid var(--primary);
        }

        .channel-data-content {
            background: var(--neutrals-gray800);
            padding: 15px;
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals-gray700);
            max-height: 500px;
            overflow-y: auto;
        }

        .channel-table {
            width: 100%;
            border-collapse: collapse;
            font-size: 0.9em;
        }

        .channel-table th,
        .channel-table td {
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid var(--neutrals-gray700);
        }

        .channel-table th {
            background: var(--neutrals-gray700);
            font-weight: 600;
            color: var(--text-primary);
            width: 40%;
        }

        .channel-table td {
            color: var(--text-secondary);
            font-family: 'Courier New', monospace;
        }

        .channel-table tr:hover {
            background: var(--neutrals-gray600);
        }

        .channel-summary {
            display: flex;
            flex-wrap: nowrap;
            gap: 10px;
            margin-bottom: 20px;
            overflow-x: auto;
        }

        .channel-summary .summary-card {
            flex: 1 1 0;
            min-width: 120px;
            max-width: none;
        }

        /* 确保在大屏幕上6个卡片横向排列 */
        @media (min-width: 1200px) {
            .channel-summary {
                flex-wrap: nowrap;
            }

            .channel-summary .summary-card {
                flex: 1;
                min-width: 140px;
            }
        }

        /* 中等屏幕允许换行 */
        @media (max-width: 1199px) and (min-width: 800px) {
            .channel-summary {
                flex-wrap: wrap;
            }

            .channel-summary .summary-card {
                flex: 1 1 calc(33.333% - 10px);
                min-width: 150px;
            }
        }

        /* 小屏幕2列 */
        @media (max-width: 799px) and (min-width: 500px) {
            .channel-summary {
                flex-wrap: wrap;
            }

            .channel-summary .summary-card {
                flex: 1 1 calc(50% - 10px);
                min-width: 140px;
            }
        }

        /* 手机屏幕1列 */
        @media (max-width: 499px) {
            .channel-summary {
                flex-direction: column;
                flex-wrap: nowrap;
            }

            .channel-summary .summary-card {
                flex: none;
                min-width: auto;
            }
        }

        .summary-card {
            background: var(--neutrals-gray800);
            padding: 16px 12px;
            border-radius: var(--border-radius);
            border: 1px solid var(--neutrals-gray700);
            text-align: center;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            min-height: 85px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }

        .summary-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, var(--primary) 0%, var(--secondary) 100%);
        }

        .summary-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 25px rgba(34, 211, 139, 0.15);
            border-color: var(--primary);
        }

        .summary-value {
            font-size: 1.1em;
            font-weight: bold;
            color: var(--primary);
            margin-bottom: 4px;
            word-break: break-word;
            line-height: 1.2;
        }

        .summary-label {
            font-size: 0.8em;
            color: var(--text-secondary);
            line-height: 1.1;
        }

        .metadata-section {
            margin-bottom: 25px;
        }

        .metadata-section-title {
            font-size: 1.1em;
            font-weight: bold;
            color: var(--text-primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--neutrals-gray700);
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            background: var(--neutrals-gray700);
            padding: 20px;
            border-radius: var(--border-radius);
            text-align: center;
            border-left: 4px solid var(--primary);
        }

        .metric-value {
            font-size: 2em;
            font-weight: bold;
            color: var(--primary);
            margin-bottom: 5px;
        }

        .metric-label {
            color: var(--text-secondary);
            font-size: 0.9em;
        }

        .visualization-container {
            text-align: center;
            margin: 30px 0;
        }

        .visualization-container img {
            max-width: 100%;
            height: auto;
            border-radius: var(--border-radius);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .loading {
            display: none;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            z-index: 1000;
            border-radius: 15px;
        }

        .loading-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .loading-text {
            font-size: 1.1em;
            color: #2c3e50;
            font-weight: 500;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            background: var(--neutrals-gray700);
            color: #ff6b6b;
            padding: 12px 15px;
            border-radius: var(--border-radius);
            margin: 15px 0;
            border: 1px solid #ff6b6b;
            display: none;
            font-size: 0.9em;
        }

        .success-message {
            background: var(--neutrals-gray700);
            color: var(--primary);
            padding: 12px 15px;
            border-radius: var(--border-radius);
            margin: 15px 0;
            border: 1px solid var(--primary);
            display: none;
            font-size: 0.9em;
        }

        .footer {
            background: var(--neutrals-gray800);
            color: var(--text-primary);
            text-align: center;
            padding: 20px;
            font-size: 0.9em;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #28a745;
        }

        .status-offline {
            background: #dc3545;
        }

        .anomaly-high {
            border-left-color: #dc3545 !important;
        }

        .anomaly-medium {
            border-left-color: #ffc107 !important;
        }

        .anomaly-low {
            border-left-color: #28a745 !important;
        }

        .default-message {
            text-align: center;
            padding: 60px 40px;
            color: var(--text-secondary);
        }

        .default-message h3 {
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .default-message p {
            font-size: 1.1em;
            line-height: 1.6;
            color: var(--text-secondary);
        }

        .default-message .format-info {
            margin-top: 30px;
            padding: 20px;
            background: var(--neutrals-gray700);
            border-radius: var(--border-radius);
            border-left: 4px solid var(--secondary);
            color: var(--text-primary);
        }

        .loading-text {
            color: var(--text-primary);
            font-size: 1.1em;
            margin-top: 15px;
        }

        .crash-test-icon {
            margin-bottom: 25px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .crash-test-icon svg {
            filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
            transition: transform 0.3s ease;
        }

        .crash-test-icon:hover svg {
            transform: scale(1.05);
        }

        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }

            .left-panel, .right-panel {
                min-width: auto;
            }
        }

        @media (max-width: 1200px) {
            .main-content {
                flex-direction: column;
            }

            .left-panel, .right-panel {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>碰撞测试数据异常检测系统 演示Demo </h1>
            <p>基于高级LSTM自编码器的智能异常检测技术验证POC</p>
            <p><span class="status-indicator status-offline" id="statusIndicator"></span><span id="statusText">连接中...</span></p>
        </div>

        <div class="main-content">
            <!-- 左侧面板 - 输入区域 -->
            <div class="left-panel">
                <!-- 文件上传区域 -->
                <div class="upload-section">
                    <div class="upload-area">
                        <div class="upload-icon">📁</div>
                        <h3>上传碰撞测试数据文件</h3>
                        <p>支持格式：CSV、TXT、.001 文件</p>
                        <input type="file" id="fileInput" class="file-input" accept=".csv,.txt,.001">
                        <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                            选择文件
                        </button>
                    </div>

                    <div class="file-info" id="fileInfo">
                        <h4>已选择文件：</h4>
                        <p id="fileName"></p>
                        <p id="fileSize"></p>
                        <button class="detect-btn" id="detectBtn" onclick="detectAnomalies()">
                            🔍 开始异常检测
                        </button>
                    </div>
                </div>

                <!-- 错误消息 -->
                <div class="error-message" id="errorMessage"></div>

                <!-- 成功消息 -->
                <div class="success-message" id="successMessage"></div>
            </div>

            <!-- 右侧面板 - 输出区域 -->
            <div class="right-panel">
                <!-- 加载状态 -->
                <div class="loading" id="loading">
                    <div class="loading-content">
                        <div class="spinner"></div>
                        <div class="loading-text">正在进行异常检测分析，请稍候...</div>
                    </div>
                </div>

                <!-- 默认提示信息 -->
                <div class="default-message" id="defaultMessage">
                    <div style="font-size: 4em; margin-bottom: 20px;">🚗💥</div>
                    <h3>碰撞测试异常检测结果展示区域</h3>
                    <p>
                        请在左侧选择碰撞测试数据文件并点击"开始异常检测"按钮<br>
                        AI将分析传感器数据中的异常模式并在此处显示结果
                    </p>
                    <div class="format-info">
                        <strong>支持的文件格式：</strong><br>
                        CSV、TXT、.001 文件
                    </div>
                </div>

                <!-- 检测结果区域 -->
                <div class="results-section" id="resultsSection">
                    <div class="results-header">
                        <h2 class="results-title">🎯 异常检测结果</h2>
                        <div class="file-name-display" id="fileNameDisplay" style="display: none;"></div>
                    </div>

                    <div class="tab-container">
                        <!-- 标签页导航 -->
                        <div class="tab-nav">
                            <button class="tab-button active" onclick="switchTab('results')">
                                📊 检测结果
                            </button>
                            <button class="tab-button" onclick="switchTab('metadata')">
                                📋 元数据信息
                            </button>
                        </div>

                        <!-- 检测结果标签页 -->
                        <div class="tab-content active" id="resultsTab">
                            <div class="metrics-grid">
                                <div class="metric-card" id="dataPointsCard">
                                    <div class="metric-value" id="dataPoints">-</div>
                                    <div class="metric-label">数据点数量</div>
                                </div>
                                <div class="metric-card" id="anomalyCountCard">
                                    <div class="metric-value" id="anomalyCount">-</div>
                                    <div class="metric-label">异常序列数</div>
                                </div>
                                <div class="metric-card" id="totalSequencesCard">
                                    <div class="metric-value" id="totalSequences">-</div>
                                    <div class="metric-label">总序列数</div>
                                </div>
                                <div class="metric-card" id="anomalyPercentageCard">
                                    <div class="metric-value" id="anomalyPercentage">-</div>
                                    <div class="metric-label">异常比例 (%)</div>
                                </div>
                            </div>

                            <div class="visualization-container">
                                <h3>📊 异常检测可视化结果</h3>
                                <img id="visualizationImage" src="" alt="异常检测可视化图表" style="display: none;">
                            </div>
                        </div>

                        <!-- 元数据信息标签页 -->
                        <div class="tab-content" id="metadataTab">
                            <!-- 文件元数据信息 -->
                            <div class="metadata-section">
                                <div class="metadata-section-title">💾 文件元数据信息</div>
                                <div class="file-metadata" id="fileMetadata" style="display: none;">
                                    <div class="metadata-header" onclick="toggleFileMetadata()">
                                        <div class="metadata-title">📋 文件基本信息</div>
                                        <button class="toggle-btn" id="fileToggleBtn">展开</button>
                                    </div>
                                    <div class="metadata-summary" id="metadataSummary"></div>
                                    <div class="metadata-content" id="metadataContent"></div>
                                </div>
                            </div>

                            <!-- 通道数据元信息 -->
                            <div class="metadata-section">
                                <div class="metadata-section-title">📡 通道数据元信息</div>

                                <!-- 通道摘要卡片 -->
                                <div class="channel-summary" id="channelSummary"></div>

                                <div class="channel-metadata" id="channelMetadata" style="display: none;">
                                    <div class="metadata-header" onclick="toggleChannelMetadata()">
                                        <div class="metadata-title">📊 通道详细参数表</div>
                                        <button class="toggle-btn" id="channelToggleBtn">展开</button>
                                    </div>
                                    <div class="channel-data-content" id="channelDataContent"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>© 2025 中汽数据（天津）有限公司 技术验证：刁国亮 (<EMAIL>)</p>
            <p>碰撞测试数据异常AI智能检测技术验证POC项目</p>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:5000';
        let selectedFile = null;

        // 页面加载时检查API服务状态
        window.onload = function() {
            checkApiStatus();
        };

        // 检查API服务状态
        async function checkApiStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/health`);
                if (response.ok) {
                    document.getElementById('statusIndicator').className = 'status-indicator status-online';
                    document.getElementById('statusText').textContent = '服务在线';
                } else {
                    throw new Error('服务响应异常');
                }
            } catch (error) {
                document.getElementById('statusIndicator').className = 'status-indicator status-offline';
                document.getElementById('statusText').textContent = '服务离线';
                showError('无法连接到API服务，请确保服务已启动');
            }
        }

        // 文件选择处理
        document.getElementById('fileInput').addEventListener('change', function(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                document.getElementById('fileName').textContent = file.name;
                document.getElementById('fileSize').textContent = `文件大小: ${(file.size / 1024).toFixed(2)} KB`;
                document.getElementById('fileInfo').style.display = 'block';
                document.getElementById('detectBtn').style.display = 'inline-block';

                // 更新结果标题中的文件名
                document.getElementById('fileNameDisplay').textContent = file.name;
                document.getElementById('fileNameDisplay').style.display = 'block';

                // 解析通道数据元信息
                parseChannelMetadata(file);

                hideMessages();
            }
        });

        // 异常检测函数
        async function detectAnomalies() {
            if (!selectedFile) {
                showError('请先选择文件');
                return;
            }

            // 记录开始时间
            window.startTime = new Date();

            // 显示加载状态
            document.getElementById('loading').style.display = 'block';
            document.getElementById('defaultMessage').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('detectBtn').disabled = true;
            hideMessages();

            try {
                const formData = new FormData();
                formData.append('file', selectedFile);

                const response = await fetch(`${API_BASE_URL}/detect`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    displayResults(result);
                    showSuccess('异常检测完成！');
                } else {
                    throw new Error(result.error || '检测失败');
                }
            } catch (error) {
                showError(`检测失败: ${error.message}`);
            } finally {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('detectBtn').disabled = false;
            }
        }

        // 显示检测结果
        function displayResults(result) {
            // 更新指标卡片
            document.getElementById('dataPoints').textContent = result.data_points.toLocaleString();
            document.getElementById('anomalyCount').textContent = result.anomaly_count;
            document.getElementById('totalSequences').textContent = result.total_sequences;
            document.getElementById('anomalyPercentage').textContent = result.anomaly_percentage + '%';

            // 根据异常比例设置卡片颜色
            const anomalyCard = document.getElementById('anomalyPercentageCard');
            if (result.anomaly_percentage > 10) {
                anomalyCard.className = 'metric-card anomaly-high';
            } else if (result.anomaly_percentage > 5) {
                anomalyCard.className = 'metric-card anomaly-medium';
            } else {
                anomalyCard.className = 'metric-card anomaly-low';
            }

            // 显示文件元数据
            displayFileMetadata(result);

            // 显示可视化图表
            if (result.visualization) {
                const img = document.getElementById('visualizationImage');
                img.src = `data:image/png;base64,${result.visualization}`;
                img.style.display = 'block';
            }

            // 显示结果区域，隐藏默认消息
            document.getElementById('defaultMessage').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'block';
        }

        // 显示文件元数据
        function displayFileMetadata(result) {
            const metadataDiv = document.getElementById('fileMetadata');
            const summaryDiv = document.getElementById('metadataSummary');
            const contentDiv = document.getElementById('metadataContent');

            // 创建元数据摘要
            const summaryData = [
                { label: '文件名', value: result.filename },
                { label: '数据点数', value: result.data_points.toLocaleString() },
                { label: '文件大小', value: `${(selectedFile.size / 1024).toFixed(2)} KB` },
                { label: '处理时间', value: `${((new Date() - window.startTime) / 1000).toFixed(2)}秒` }
            ];

            summaryDiv.innerHTML = summaryData.map(item =>
                `<div class="metadata-item">
                    <span class="metadata-label">${item.label}:</span>
                    <span class="metadata-value">${item.value}</span>
                </div>`
            ).join('');

            // 创建详细元数据内容
            const detailData = {
                '文件信息': {
                    '原始文件名': result.filename,
                    '文件大小': `${selectedFile.size} bytes (${(selectedFile.size / 1024).toFixed(2)} KB)`,
                    '文件类型': selectedFile.type || '未知',
                    '最后修改': selectedFile.lastModified ? new Date(selectedFile.lastModified).toLocaleString() : '未知'
                },
                '检测参数': {
                    '序列长度': '100',
                    '滑动步长': '10',
                    '模型类型': '高级LSTM自编码器',
                    '检测阈值': '动态计算'
                },
                '处理结果': {
                    '数据点数量': result.data_points.toLocaleString(),
                    '序列总数': result.total_sequences,
                    '异常序列数': result.anomaly_count,
                    '异常比例': `${result.anomaly_percentage}%`,
                    '处理时间': `${((new Date() - window.startTime) / 1000).toFixed(2)}秒`
                }
            };

            let contentHtml = '';
            for (const [category, items] of Object.entries(detailData)) {
                contentHtml += `<div style="margin-bottom: 15px;">`;
                contentHtml += `<div style="font-weight: bold; color: #17a2b8; margin-bottom: 5px;">${category}:</div>`;
                for (const [key, value] of Object.entries(items)) {
                    contentHtml += `<div style="margin-left: 10px; margin-bottom: 2px;">`;
                    contentHtml += `<span style="color: #6c757d;">${key}:</span> `;
                    contentHtml += `<span style="color: #2c3e50; font-weight: 500;">${value}</span>`;
                    contentHtml += `</div>`;
                }
                contentHtml += `</div>`;
            }

            contentDiv.innerHTML = contentHtml;
            metadataDiv.style.display = 'block';
        }

        // 标签页切换
        function switchTab(tabName) {
            // 移除所有活动状态
            document.querySelectorAll('.tab-button').forEach(btn => btn.classList.remove('active'));
            document.querySelectorAll('.tab-content').forEach(content => content.classList.remove('active'));

            // 激活选中的标签页
            event.target.classList.add('active');
            document.getElementById(tabName + 'Tab').classList.add('active');
        }

        // 切换文件元数据显示
        function toggleFileMetadata() {
            const content = document.getElementById('metadataContent');
            const toggleBtn = document.getElementById('fileToggleBtn');

            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggleBtn.textContent = '收起';
            } else {
                content.style.display = 'none';
                toggleBtn.textContent = '展开';
            }
        }

        // 切换通道元数据显示
        function toggleChannelMetadata() {
            const content = document.getElementById('channelDataContent');
            const toggleBtn = document.getElementById('channelToggleBtn');

            if (content.style.display === 'none' || content.style.display === '') {
                content.style.display = 'block';
                toggleBtn.textContent = '收起';
            } else {
                content.style.display = 'none';
                toggleBtn.textContent = '展开';
            }
        }

        // 解析通道数据元信息
        async function parseChannelMetadata(file) {
            try {
                const text = await file.text();
                const lines = text.split('\n');
                const fileName = file.name.toLowerCase();

                // 根据文件类型进行不同的解析
                if (fileName.endsWith('.001')) {
                    // .001格式：标准通道数据格式
                    parseStandardChannelFormat(lines);
                } else if (fileName.endsWith('.csv')) {
                    // CSV格式：分析CSV结构
                    parseCSVFormat(lines, file);
                } else if (fileName.endsWith('.txt')) {
                    // TXT格式：尝试多种解析方式
                    parseTXTFormat(lines, file);
                } else {
                    // 其他格式：通用解析
                    parseGenericFormat(lines, file);
                }

            } catch (error) {
                console.error('解析通道数据失败:', error);
                showChannelError('无法解析通道数据元信息：' + error.message);
            }
        }

        // 解析标准通道格式（.001文件）
        function parseStandardChannelFormat(lines) {
            const metadata = {};
            let dataStartIndex = 0;

            for (let i = 0; i < Math.min(lines.length, 50); i++) {
                const line = lines[i].trim();

                // 检查是否是数据行（纯数字）
                if (line.match(/^-?\d+\.?\d*([eE][+-]?\d+)?$/)) {
                    dataStartIndex = i;
                    break;
                }

                // 解析键值对
                if (line.includes(':')) {
                    const colonIndex = line.indexOf(':');
                    const key = line.substring(0, colonIndex).trim();
                    const value = line.substring(colonIndex + 1).trim();
                    if (key && value) {
                        metadata[key] = value;
                    }
                }
            }

            if (Object.keys(metadata).length > 0) {
                createChannelSummary(metadata);
                createChannelTable(metadata);
                document.getElementById('channelSummary');
                document.getElementById('channelMetadata').style.display = 'block';
            } else {
                showChannelRawData(lines.slice(0, 20), '未检测到标准元数据格式');
            }
        }

        // 解析CSV格式
        function parseCSVFormat(lines, file) {
            const metadata = {
                'File Type': 'CSV (Comma Separated Values)',
                'File Name': file.name,
                'File Size': `${(file.size / 1024).toFixed(2)} KB`,
                'Last Modified': file.lastModified ? new Date(file.lastModified).toLocaleString() : '未知'
            };

            if (lines.length > 0) {
                const firstLine = lines[0].trim();
                const headers = firstLine.split(',');

                metadata['Column Count'] = headers.length.toString();
                metadata['Data Rows'] = (lines.length - 1).toString();
                metadata['Total Lines'] = lines.length.toString();

                // 检查是否有标准的时间序列列
                if (headers.some(h => h.toLowerCase().includes('time'))) {
                    metadata['Time Column'] = 'Detected';
                }
                if (headers.some(h => h.toLowerCase().includes('value'))) {
                    metadata['Value Column'] = 'Detected';
                }

                // 显示列名
                metadata['Column Headers'] = headers.join(', ');

                // 分析数据类型
                if (lines.length > 1) {
                    const sampleData = lines[1].split(',');
                    const dataTypes = sampleData.map(val => {
                        if (!isNaN(val) && !isNaN(parseFloat(val))) {
                            return 'Number';
                        } else {
                            return 'Text';
                        }
                    });
                    metadata['Data Types'] = dataTypes.join(', ');
                }
            }

            createCSVChannelSummary(metadata);
            createChannelTable(metadata);
            document.getElementById('channelSummary').style.display = 'block';
            document.getElementById('channelMetadata').style.display = 'block';
        }

        // 解析TXT格式
        function parseTXTFormat(lines, file) {
            // 首先尝试标准通道格式
            const hasColonFormat = lines.slice(0, 20).some(line => line.includes(':'));

            if (hasColonFormat) {
                // 可能是类似.001的格式
                parseStandardChannelFormat(lines);
            } else {
                // 纯数据格式
                const metadata = {
                    'File Type': 'TXT (Plain Text)',
                    'File Name': file.name,
                    'File Size': `${(file.size / 1024).toFixed(2)} KB`,
                    'Last Modified': file.lastModified ? new Date(file.lastModified).toLocaleString() : '未知',
                    'Total Lines': lines.length.toString()
                };

                // 分析数据格式
                const nonEmptyLines = lines.filter(line => line.trim());
                metadata['Data Lines'] = nonEmptyLines.length.toString();

                // 检查是否是纯数字数据
                const numericLines = nonEmptyLines.filter(line =>
                    line.trim().match(/^-?\d+\.?\d*([eE][+-]?\d+)?$/)
                );

                if (numericLines.length > 0) {
                    metadata['Numeric Data Lines'] = numericLines.length.toString();
                    metadata['Data Format'] = 'Single Column Numeric';

                    // 计算基本统计
                    const values = numericLines.map(line => parseFloat(line.trim()));
                    metadata['Min Value'] = Math.min(...values).toExponential(3);
                    metadata['Max Value'] = Math.max(...values).toExponential(3);
                    metadata['Data Range'] = `${values.length} samples`;
                }

                createTXTChannelSummary(metadata);
                createChannelTable(metadata);
                document.getElementById('channelSummary').style.display = 'block';
                document.getElementById('channelMetadata').style.display = 'block';
            }
        }

        // 解析通用格式
        function parseGenericFormat(lines, file) {
            const metadata = {
                'File Type': 'Generic Format',
                'File Name': file.name,
                'File Size': `${(file.size / 1024).toFixed(2)} KB`,
                'Last Modified': file.lastModified ? new Date(file.lastModified).toLocaleString() : '未知',
                'Total Lines': lines.length.toString()
            };

            createGenericChannelSummary(metadata);
            showChannelRawData(lines.slice(0, 20), '通用格式文件，显示前20行内容');
        }

        // 显示通道错误信息
        function showChannelError(message) {
            document.getElementById('channelDataContent').innerHTML =
                `<div style="color: #ff6b6b; padding: 20px; text-align: center;">
                    <i style="font-size: 2em; margin-bottom: 10px;">⚠️</i><br>
                    ${message}
                </div>`;
            document.getElementById('channelMetadata').style.display = 'block';
        }

        // 显示原始数据
        function showChannelRawData(lines, title) {
            const rawData = lines.join('\n');
            document.getElementById('channelDataContent').innerHTML =
                `<div style="margin-bottom: 15px; padding: 10px; background: var(--neutrals-gray700); border-radius: var(--border-radius); color: var(--text-primary);">
                    <strong>${title}</strong>
                </div>
                <pre style="margin: 0; font-family: Courier New, monospace; font-size: 0.85em; line-height: 1.4; color: var(--text-secondary);">${rawData}</pre>`;
            document.getElementById('channelMetadata').style.display = 'block';
        }

        // 创建通道摘要卡片（.001格式）
        function createChannelSummary(metadata) {
            const summaryDiv = document.getElementById('channelSummary');

            // 提取关键信息
            const keyInfo = [
                { label: '通道名称', value: metadata['Name of the channel'] || '未知', key: 'name' },
                { label: '方向', value: metadata['Direction'] || '未知', key: 'direction' },
                { label: '单位', value: metadata['Unit'] || '未知', key: 'unit' },
                { label: '采样频率', value: metadata['Channel frequency class'] ? metadata['Channel frequency class'] + ' Hz' : '未知', key: 'frequency' },
                { label: '采样间隔', value: metadata['Sampling interval'] ? metadata['Sampling interval'] + ' s' : '未知', key: 'interval' },
                { label: '样本数量', value: metadata['Number of samples'] || '未知', key: 'samples' }
            ];

            summaryDiv.innerHTML = keyInfo.map(item =>
                `<div class="summary-card">
                    <div class="summary-value">${item.value}</div>
                    <div class="summary-label">${item.label}</div>
                </div>`
            ).join('');
        }

        // 创建CSV格式摘要卡片
        function createCSVChannelSummary(metadata) {
            const summaryDiv = document.getElementById('channelSummary');

            const keyInfo = [
                { label: '文件类型', value: 'CSV', key: 'type' },
                { label: '列数', value: metadata['Column Count'] || '未知', key: 'columns' },
                { label: '数据行数', value: metadata['Data Rows'] || '未知', key: 'rows' },
                { label: '文件大小', value: metadata['File Size'] || '未知', key: 'size' },
                { label: '时间列', value: metadata['Time Column'] || '未检测到', key: 'time' },
                { label: '数值列', value: metadata['Value Column'] || '未检测到', key: 'value' }
            ];

            summaryDiv.innerHTML = keyInfo.map(item =>
                `<div class="summary-card">
                    <div class="summary-value">${item.value}</div>
                    <div class="summary-label">${item.label}</div>
                </div>`
            ).join('');
        }

        // 创建TXT格式摘要卡片
        function createTXTChannelSummary(metadata) {
            const summaryDiv = document.getElementById('channelSummary');

            const keyInfo = [
                { label: '文件类型', value: 'TXT', key: 'type' },
                { label: '总行数', value: metadata['Total Lines'] || '未知', key: 'lines' },
                { label: '数据行数', value: metadata['Data Lines'] || '未知', key: 'datalines' },
                { label: '文件大小', value: metadata['File Size'] || '未知', key: 'size' },
                { label: '数据格式', value: metadata['Data Format'] || '未知', key: 'format' },
                { label: '数值范围', value: metadata['Data Range'] || '未知', key: 'range' }
            ];

            summaryDiv.innerHTML = keyInfo.map(item =>
                `<div class="summary-card">
                    <div class="summary-value">${item.value}</div>
                    <div class="summary-label">${item.label}</div>
                </div>`
            ).join('');
        }

        // 创建通用格式摘要卡片
        function createGenericChannelSummary(metadata) {
            const summaryDiv = document.getElementById('channelSummary');

            const keyInfo = [
                { label: '文件类型', value: '通用格式', key: 'type' },
                { label: '文件名', value: metadata['File Name'] || '未知', key: 'name' },
                { label: '总行数', value: metadata['Total Lines'] || '未知', key: 'lines' },
                { label: '文件大小', value: metadata['File Size'] || '未知', key: 'size' },
                { label: '修改时间', value: metadata['Last Modified'] || '未知', key: 'modified' },
                { label: '状态', value: '需要手动分析', key: 'status' }
            ];

            summaryDiv.innerHTML = keyInfo.map(item =>
                `<div class="summary-card">
                    <div class="summary-value">${item.value}</div>
                    <div class="summary-label">${item.label}</div>
                </div>`
            ).join('');

            document.getElementById('channelSummary').style.display = 'block';
        }

        // 创建通道详细参数表格
        function createChannelTable(metadata) {
            const contentDiv = document.getElementById('channelDataContent');

            // 按类别组织数据
            const categories = {
                '基本信息': [
                    'Test object number',
                    'Name of the channel',
                    'Laboratory channel code',
                    'Customer channel code',
                    'Channel code',
                    'Direction',
                    'Dimension'
                ],
                '技术参数': [
                    'Channel frequency class',
                    'Unit',
                    'Reference system',
                    'Sampling interval',
                    'Bit resolution',
                    'Channel amplitude class'
                ],
                '传感器信息': [
                    'Transducer type',
                    'Transducer id',
                    'Transducer natural frequency',
                    'Transducer damping ratio'
                ],
                '滤波器设置': [
                    'Pre-filter type',
                    'Cut off frequency'
                ],
                '数据信息': [
                    'Time of first sample',
                    'Number of samples',
                    'Data source',
                    'Data status'
                ],
                '统计信息': [
                    'First global maximum value',
                    'Time of maximum value',
                    'First global minimum value',
                    'Time of minimum value'
                ],
                '校准信息': [
                    '.Calibration date',
                    '.Calibration due date',
                    '.Direction polarity',
                    '.Inverse sensitivity',
                    '.Sensor offset pre test'
                ]
            };

            let tableHtml = '';

            for (const [category, keys] of Object.entries(categories)) {
                const categoryData = keys.filter(key => metadata[key]).map(key => ({
                    key: key,
                    value: metadata[key]
                }));

                if (categoryData.length > 0) {
                    tableHtml += `
                        <h4 style="color: var(--secondary); margin: 20px 0 10px 0; padding-bottom: 5px; border-bottom: 1px solid var(--neutrals-gray700);">
                            ${category}
                        </h4>
                        <table class="channel-table">
                    `;

                    categoryData.forEach(item => {
                        const displayKey = item.key.replace(/^\./, '').replace(/_/g, ' ');
                        tableHtml += `
                            <tr>
                                <th>${displayKey}</th>
                                <td>${item.value}</td>
                            </tr>
                        `;
                    });

                    tableHtml += '</table>';
                }
            }

            // 添加其他未分类的参数
            const allCategorizedKeys = Object.values(categories).flat();
            const otherKeys = Object.keys(metadata).filter(key => !allCategorizedKeys.includes(key));

            if (otherKeys.length > 0) {
                tableHtml += `
                    <h4 style="color: var(--secondary); margin: 20px 0 10px 0; padding-bottom: 5px; border-bottom: 1px solid var(--neutrals-gray700);">
                        其他参数
                    </h4>
                    <table class="channel-table">
                `;

                otherKeys.forEach(key => {
                    const displayKey = key.replace(/^\./, '').replace(/_/g, ' ');
                    tableHtml += `
                        <tr>
                            <th>${displayKey}</th>
                            <td>${metadata[key]}</td>
                        </tr>
                    `;
                });

                tableHtml += '</table>';
            }

            contentDiv.innerHTML = tableHtml;
        }

        // 显示错误消息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 显示成功消息
        function showSuccess(message) {
            const successDiv = document.getElementById('successMessage');
            successDiv.textContent = message;
            successDiv.style.display = 'block';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 隐藏消息
        function hideMessages() {
            document.getElementById('errorMessage').style.display = 'none';
            document.getElementById('successMessage').style.display = 'none';
        }

        // 定期检查服务状态
        setInterval(checkApiStatus, 30000); // 每30秒检查一次
    </script>
</body>
</html>
