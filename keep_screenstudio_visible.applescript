-- Screen Studio界面保持可见脚本
-- 使用方法：在录制前运行此脚本

on run
    set appName to "Screen Studio"
    
    -- 检查Screen Studio是否正在运行
    tell application "System Events"
        if not (exists process appName) then
            display dialog "Screen Studio未运行，请先启动Screen Studio" buttons {"确定"} default button 1
            return
        end if
    end tell
    
    -- 主循环：持续监控并显示窗口
    repeat
        try
            tell application appName
                -- 激活应用程序
                activate
                
                -- 显示所有窗口
                set visible of every window to true
                
                -- 将主窗口置于前台
                if (count of windows) > 0 then
                    set index of window 1 to 1
                end if
            end tell
            
            -- 等待1秒后再次检查
            delay 1
            
        on error errMsg
            -- 如果出错，记录错误并继续
            log "错误: " & errMsg
            delay 2
        end try
        
        -- 检查是否需要停止脚本（可以通过按Cmd+. 停止）
        
    end repeat
end run
