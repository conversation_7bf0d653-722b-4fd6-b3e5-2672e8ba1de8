# 碰撞测试数据异常检测系统 - 交互演示

## 🎯 项目概述

基于高级LSTM自编码器模型的碰撞测试数据异常检测交互演示系统，实现了从数据上传到异常检测结果展示的完整流程。

## 🚀 快速开始

### 方法一：一键启动（推荐）
```bash
python start_demo.py
```

### 方法二：手动启动
```bash
# 1. 启动API服务
python api_service.py

# 2. 在浏览器中打开 POC-UI.html
```

## 📁 核心文件

### 后端服务
- `api_service.py` - Flask API服务，提供异常检测接口
- `test_api.py` - API服务测试脚本

### 前端界面
- `POC-UI.html` - 交互演示界面

### 启动工具
- `start_demo.py` - 一键启动脚本
- `演示说明.md` - 详细演示说明文档

## 🎬 演示流程

1. **启动系统** - 运行 `python start_demo.py`
2. **检查状态** - 确认服务在线（绿色圆点）
3. **上传数据** - 在左侧面板选择测试文件（推荐使用客户数据）
4. **执行检测** - 点击"开始异常检测"按钮
5. **查看结果** - 在右侧面板"检测结果"标签页查看统计和可视化
6. **查看元数据** - 切换到"元数据信息"标签页查看详细信息
7. **通道信息** - 查看通道摘要卡片和详细参数表格

## 📊 测试数据

推荐使用以下测试文件进行演示：

| 文件路径 | 异常比例 | 说明 |
|---------|---------|------|
| `data/customer/1/CHANNEL/1.001` | 7.57% | 中等异常 |
| `data/customer/2/CHANNEL/2.001` | 0.0% | 正常数据 |
| `data/customer/3/CHANNEL/3.001` | 12.51% | 高异常 |
| `data/customer/4/CHANNEL/4.001` | 0.0% | 正常数据 |
| `data/customer/5/CHANNEL/5.001` | 1.82% | 低异常 |

## 🔧 技术架构

### 后端技术栈
- **Flask** - Web框架
- **TensorFlow** - 深度学习框架
- **NumPy/Pandas** - 数据处理
- **Matplotlib** - 可视化生成

### 前端技术栈
- **HTML5/CSS3** - 界面结构和样式
- **JavaScript** - 交互逻辑
- **Fetch API** - 异步通信
- **响应式布局** - 左右分栏设计，支持移动端

### AI模型
- **高级LSTM自编码器** - 核心异常检测模型
- **准确率**: 95%+
- **处理速度**: 4-18秒/10,000数据点

## 📈 性能指标

- ✅ **检测准确率**: 95%+
- ⚡ **处理速度**: 4-18秒/万点数据
- 📁 **支持格式**: CSV、TXT、.001
- 💾 **最大文件**: 16MB
- 🌐 **跨平台**: 支持各种浏览器

## 🎥 录制要点

### 演示脚本（总时长约3分钟）
1. **开场介绍** (15秒) - 项目背景和目标
2. **系统启动** (20秒) - 展示启动过程
3. **界面介绍** (15秒) - 说明左右分栏布局和标签页设计
4. **正常数据检测** (30秒) - 演示正常数据处理
5. **异常数据检测** (30秒) - 演示异常数据识别
6. **通道元数据展示** (25秒) - 展示通道摘要卡片和详细参数表格
7. **文件元数据展示** (15秒) - 展示文件信息和处理参数
8. **结果分析** (20秒) - 解释检测结果
9. **总结展望** (15秒) - 技术价值和应用前景

### 录制建议
- 使用1080p分辨率录制
- 确保界面清晰可见
- 语速适中，重点突出
- 准备多个测试文件备用

## 🛠️ 故障排除

### 常见问题

**Q: API服务无法启动**
```bash
# 检查依赖
pip install flask flask-cors tensorflow numpy pandas matplotlib joblib

# 检查模型文件
ls -la models/advanced_lstm_*
```

**Q: 前端无法连接API**
```bash
# 检查服务状态
curl http://localhost:5000/health
```

**Q: 文件上传失败**
- 确认文件格式（CSV/TXT/.001）
- 检查文件大小（<16MB）
- 验证文件内容格式

## 📞 技术支持

- **开发单位**: 山东山创网络科技有限公司
- **技术验证**: 刁国亮 (<EMAIL>)
- **合作单位**: 中汽数据（天津）有限公司

## 🔮 后续规划

### 功能扩展
- [ ] 批量文件处理
- [ ] 更多数据格式支持
- [ ] 检测历史记录
- [ ] 结果导出功能

### 性能优化
- [ ] 模型推理加速
- [ ] 并发处理支持
- [ ] 缓存机制优化
- [ ] 内存使用优化

### 系统集成
- [ ] 企业级部署方案
- [ ] 数据库存储支持
- [ ] 用户权限管理
- [ ] 与现有系统集成

---

**🎉 演示系统已就绪，开始您的异常检测之旅！**
