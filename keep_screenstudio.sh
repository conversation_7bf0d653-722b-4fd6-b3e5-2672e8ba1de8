#!/bin/bash

# Screen Studio界面保持脚本
# 使用方法: ./keep_screenstudio.sh

APP_NAME="Screen Studio"
CHECK_INTERVAL=1

echo "🎬 Screen Studio界面保持工具启动"
echo "📱 监控应用: $APP_NAME"
echo "⏱️  检查间隔: ${CHECK_INTERVAL}秒"
echo "按 Ctrl+C 停止监控"
echo ""

# 信号处理
trap 'echo -e "\n🛑 监控已停止"; exit 0' INT TERM

# 检查应用是否运行
check_app_running() {
    pgrep -f "$APP_NAME" > /dev/null 2>&1
    return $?
}

# 显示窗口
show_windows() {
    osascript -e "
    tell application \"$APP_NAME\"
        activate
        set visible of every window to true
        if (count of windows) > 0 then
            set index of window 1 to 1
        end if
    end tell
    " > /dev/null 2>&1
    return $?
}

# 主循环
while true; do
    if ! check_app_running; then
        echo "⚠️  $APP_NAME 未运行，等待启动..."
        sleep $((CHECK_INTERVAL * 2))
        continue
    fi
    
    if show_windows; then
        echo -ne "✅ 窗口已显示\r"
    else
        echo -ne "❌ 显示窗口失败\r"
    fi
    
    sleep $CHECK_INTERVAL
done
