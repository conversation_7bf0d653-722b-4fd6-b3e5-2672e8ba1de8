#!/usr/bin/env python3
"""
简单测试 - 检查Screen Studio状态
"""

import subprocess
import time

def test_screen_studio():
    print("🧪 Screen Studio 状态测试")
    print("=" * 40)
    
    # 1. 检查进程
    print("1️⃣ 检查进程状态...")
    try:
        result = subprocess.run(["pgrep", "-f", "Screen Studio"], capture_output=True, text=True)
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"✅ Screen Studio 正在运行 (PIDs: {', '.join(pids)})")
        else:
            print("❌ Screen Studio 未运行")
            return
    except Exception as e:
        print(f"❌ 检查进程失败: {e}")
        return
    
    # 2. 检查应用信息
    print("\n2️⃣ 检查应用信息...")
    try:
        result = subprocess.run([
            "osascript", "-e",
            'tell application "System Events" to get name of every process whose name contains "Screen"'
        ], capture_output=True, text=True, timeout=3)
        
        if result.returncode == 0:
            print(f"✅ 系统进程: {result.stdout.strip()}")
        else:
            print(f"❌ 获取进程信息失败: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⚠️ 获取进程信息超时（可能需要权限）")
    except Exception as e:
        print(f"❌ 获取进程信息失败: {e}")
    
    # 3. 尝试简单的activate
    print("\n3️⃣ 测试应用激活...")
    try:
        result = subprocess.run([
            "osascript", "-e",
            'tell application "Screen Studio" to activate'
        ], capture_output=True, text=True, timeout=3)
        
        if result.returncode == 0:
            print("✅ 应用激活成功")
        else:
            print(f"❌ 应用激活失败: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⚠️ 应用激活超时")
    except Exception as e:
        print(f"❌ 应用激活失败: {e}")
    
    # 4. 检查窗口
    print("\n4️⃣ 检查窗口状态...")
    try:
        result = subprocess.run([
            "osascript", "-e",
            '''
            tell application "System Events"
                tell process "Screen Studio"
                    return count of windows
                end tell
            end tell
            '''
        ], capture_output=True, text=True, timeout=3)
        
        if result.returncode == 0:
            window_count = result.stdout.strip()
            print(f"✅ 窗口数量: {window_count}")
        else:
            print(f"❌ 获取窗口信息失败: {result.stderr}")
    except subprocess.TimeoutExpired:
        print("⚠️ 获取窗口信息超时（需要辅助功能权限）")
    except Exception as e:
        print(f"❌ 获取窗口信息失败: {e}")
    
    # 5. 使用open命令
    print("\n5️⃣ 测试open命令...")
    try:
        result = subprocess.run(["open", "-a", "Screen Studio"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ open命令执行成功")
        else:
            print(f"❌ open命令失败: {result.stderr}")
    except Exception as e:
        print(f"❌ open命令失败: {e}")
    
    print("\n" + "=" * 40)
    print("📋 测试完成!")
    print("\n💡 如果看到权限相关错误，请:")
    print("   1. 打开 系统偏好设置")
    print("   2. 进入 安全性与隐私 > 隐私")
    print("   3. 选择 辅助功能")
    print("   4. 添加 终端 或 Python")

if __name__ == "__main__":
    test_screen_studio()
