#!/usr/bin/env python3
"""
Screen Studio界面保持可见工具 - 改进版
使用系统级窗口管理API
"""

import subprocess
import time
import signal
import sys
import os

class ScreenStudioFix:
    def __init__(self):
        self.app_name = "Screen Studio"
        self.running = True
        
    def get_screen_studio_windows(self):
        """获取Screen Studio的窗口ID"""
        try:
            # 使用yabai或其他窗口管理工具（如果安装了）
            result = subprocess.run([
                "osascript", "-e",
                f'''
                tell application "System Events"
                    tell process "{self.app_name}"
                        set windowList to every window
                        set windowInfo to {{}}
                        repeat with w in windowList
                            set end of windowInfo to (id of w)
                        end repeat
                        return windowInfo
                    end tell
                end tell
                '''
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print(f"窗口信息: {result.stdout.strip()}")
                return result.stdout.strip()
            else:
                print(f"获取窗口失败: {result.stderr}")
                return None
                
        except Exception as e:
            print(f"错误: {e}")
            return None
    
    def bring_to_front_simple(self):
        """简单的置前方法"""
        try:
            # 方法1: 使用activate
            result = subprocess.run([
                "osascript", "-e",
                f'tell application "{self.app_name}" to activate'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            
            # 方法2: 使用System Events点击Dock
            result = subprocess.run([
                "osascript", "-e",
                f'''
                tell application "System Events"
                    tell dock preferences
                        set dock_apps to name of every dock item
                        if "{self.app_name}" is in dock_apps then
                            tell dock item "{self.app_name}" to perform action "AXPress"
                        end if
                    end tell
                end tell
                '''
            ], capture_output=True, text=True)
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"置前失败: {e}")
            return False
    
    def check_if_hidden(self):
        """检查Screen Studio是否被隐藏"""
        try:
            result = subprocess.run([
                "osascript", "-e",
                f'''
                tell application "System Events"
                    tell process "{self.app_name}"
                        return visible
                    end tell
                end tell
                '''
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                is_visible = result.stdout.strip().lower() == "true"
                return not is_visible  # 返回是否被隐藏
            
        except Exception as e:
            print(f"检查可见性失败: {e}")
            
        return False
    
    def unhide_app(self):
        """取消隐藏应用"""
        try:
            # 方法1: 使用System Events
            result = subprocess.run([
                "osascript", "-e",
                f'''
                tell application "System Events"
                    tell process "{self.app_name}"
                        set visible to true
                    end tell
                end tell
                '''
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return True
            
            # 方法2: 使用命令行工具
            subprocess.run(["open", "-a", self.app_name], check=False)
            return True
            
        except Exception as e:
            print(f"取消隐藏失败: {e}")
            return False
    
    def is_app_running(self):
        """检查应用是否运行"""
        try:
            result = subprocess.run(
                ["pgrep", "-f", self.app_name],
                capture_output=True
            )
            return result.returncode == 0
        except:
            return False
    
    def signal_handler(self, signum, frame):
        """信号处理"""
        print("\n🛑 停止监控...")
        self.running = False
        sys.exit(0)
    
    def run(self):
        """主运行循环"""
        print("🎬 Screen Studio界面保持工具 - 改进版")
        print("=" * 50)
        print(f"📱 监控应用: {self.app_name}")
        print("🔧 使用系统级窗口管理")
        print("按 Ctrl+C 停止监控\n")
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        while self.running:
            try:
                if not self.is_app_running():
                    print("⚠️  Screen Studio 未运行", end="\r")
                    time.sleep(2)
                    continue
                
                # 检查是否被隐藏
                if self.check_if_hidden():
                    print("🔍 检测到应用被隐藏，正在恢复...", end="\r")
                    if self.unhide_app():
                        print("✅ 应用已恢复显示              ", end="\r")
                    else:
                        print("❌ 恢复显示失败              ", end="\r")
                else:
                    # 确保应用在前台
                    if self.bring_to_front_simple():
                        print("✅ 应用保持在前台            ", end="\r")
                    else:
                        print("⚠️  无法置于前台              ", end="\r")
                
                time.sleep(1)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"\n运行错误: {e}")
                time.sleep(2)

def main():
    """主函数"""
    # 检查是否有必要的权限
    print("🔐 检查系统权限...")
    
    # 测试AppleScript权限
    try:
        result = subprocess.run([
            "osascript", "-e", "tell application \"System Events\" to get name of every process"
        ], capture_output=True, text=True, timeout=5)
        
        if result.returncode != 0:
            print("❌ 需要辅助功能权限!")
            print("请前往: 系统偏好设置 > 安全性与隐私 > 隐私 > 辅助功能")
            print("添加终端或Python到允许列表")
            return
        else:
            print("✅ 权限检查通过")
    
    except subprocess.TimeoutExpired:
        print("❌ 权限检查超时，可能需要授权")
        return
    except Exception as e:
        print(f"❌ 权限检查失败: {e}")
        return
    
    # 启动监控
    fixer = ScreenStudioFix()
    fixer.run()

if __name__ == "__main__":
    main()
