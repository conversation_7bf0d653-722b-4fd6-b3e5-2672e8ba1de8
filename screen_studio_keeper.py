#!/usr/bin/env python3
"""
Screen Studio界面保持可见工具
功能：防止Screen Studio在录制时自动隐藏界面
作者：AI Assistant
使用方法：python3 screen_studio_keeper.py
"""

import time
import subprocess
import sys
import signal
from typing import List, Dict, Optional

class ScreenStudioKeeper:
    def __init__(self):
        self.app_name = "Screen Studio"
        self.running = True
        self.check_interval = 1.0  # 检查间隔（秒）
        
    def is_app_running(self) -> bool:
        """检查Screen Studio是否正在运行"""
        try:
            result = subprocess.run(
                ["pgrep", "-f", self.app_name], 
                capture_output=True, 
                text=True
            )
            return result.returncode == 0
        except Exception as e:
            print(f"检查应用状态时出错: {e}")
            return False
    
    def get_window_info(self) -> List[Dict]:
        """获取Screen Studio窗口信息"""
        try:
            # 使用osascript获取窗口信息
            script = f'''
            tell application "System Events"
                tell process "{self.app_name}"
                    set windowList to every window
                    set windowInfo to {{}}
                    repeat with w in windowList
                        set end of windowInfo to {{name of w, visible of w}}
                    end repeat
                    return windowInfo
                end tell
            end tell
            '''
            
            result = subprocess.run(
                ["osascript", "-e", script],
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                return self.parse_window_info(result.stdout.strip())
            else:
                return []
                
        except Exception as e:
            print(f"获取窗口信息时出错: {e}")
            return []
    
    def parse_window_info(self, output: str) -> List[Dict]:
        """解析窗口信息输出"""
        # 简化的解析，实际可能需要更复杂的处理
        windows = []
        if output:
            # 这里需要根据实际输出格式进行解析
            print(f"窗口信息: {output}")
        return windows
    
    def show_windows(self) -> bool:
        """显示Screen Studio所有窗口"""
        try:
            script = f'''
            tell application "{self.app_name}"
                activate
                set visible of every window to true
                if (count of windows) > 0 then
                    set index of window 1 to 1
                end if
            end tell
            '''
            
            result = subprocess.run(
                ["osascript", "-e", script],
                capture_output=True,
                text=True
            )
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"显示窗口时出错: {e}")
            return False
    
    def bring_to_front(self) -> bool:
        """将Screen Studio置于前台"""
        try:
            script = f'''
            tell application "{self.app_name}"
                activate
            end tell
            '''
            
            result = subprocess.run(
                ["osascript", "-e", script],
                capture_output=True,
                text=True
            )
            
            return result.returncode == 0
            
        except Exception as e:
            print(f"置于前台时出错: {e}")
            return False
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print("\n收到停止信号，正在退出...")
        self.running = False
    
    def run(self):
        """主运行循环"""
        print(f"🎬 Screen Studio界面保持工具启动")
        print(f"📱 监控应用: {self.app_name}")
        print(f"⏱️  检查间隔: {self.check_interval}秒")
        print("按 Ctrl+C 停止监控\n")
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        while self.running:
            try:
                if not self.is_app_running():
                    print(f"⚠️  {self.app_name} 未运行，等待启动...")
                    time.sleep(self.check_interval * 2)
                    continue
                
                # 显示窗口
                if self.show_windows():
                    print("✅ 窗口已显示", end="\r")
                else:
                    print("❌ 显示窗口失败", end="\r")
                
                time.sleep(self.check_interval)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"\n运行时出错: {e}")
                time.sleep(self.check_interval)
        
        print("\n🛑 监控已停止")

def main():
    """主函数"""
    print("=" * 50)
    print("🎯 Screen Studio界面保持工具")
    print("=" * 50)
    
    keeper = ScreenStudioKeeper()
    
    try:
        keeper.run()
    except Exception as e:
        print(f"程序运行出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
