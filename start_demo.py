# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-07-08
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
演示系统快速启动脚本
自动启动API服务并打开演示界面
"""

import os
import sys
import time
import subprocess
import webbrowser
import requests
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    print("🔍 检查依赖包...")
    
    required_packages = ['flask', 'flask-cors', 'tensorflow', 'numpy', 'pandas', 'matplotlib', 'joblib']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_model_files():
    """检查模型文件"""
    print("\n🔍 检查模型文件...")
    
    required_files = [
        'models/advanced_lstm_autoencoder.h5',
        'models/advanced_lstm_scaler.pkl',
        'models/advanced_lstm_threshold.npy'
    ]
    
    missing_files = []
    for file_path in required_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"✅ {file_path} ({size:,} bytes)")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path}")
    
    if missing_files:
        print(f"\n⚠️  缺少模型文件: {', '.join(missing_files)}")
        print("请确保模型文件存在于models目录中")
        return False
    
    print("✅ 所有模型文件已就绪")
    return True

def check_test_data():
    """检查测试数据"""
    print("\n🔍 检查测试数据...")
    
    test_data_dir = 'data/customer'
    if not os.path.exists(test_data_dir):
        print(f"❌ 测试数据目录不存在: {test_data_dir}")
        return False
    
    # 检查是否有测试文件
    test_files = []
    for i in range(1, 6):
        test_file = f'data/customer/{i}/CHANNEL/{i}.001'
        if os.path.exists(test_file):
            test_files.append(test_file)
            print(f"✅ {test_file}")
    
    if not test_files:
        print("❌ 未找到测试数据文件")
        return False
    
    print(f"✅ 找到 {len(test_files)} 个测试文件")
    return True

def start_api_service():
    """启动API服务"""
    print("\n🚀 启动API服务...")
    
    try:
        # 启动API服务进程
        process = subprocess.Popen([sys.executable, 'api_service.py'], 
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE,
                                 text=True)
        
        # 等待服务启动
        print("⏳ 等待服务启动...")
        max_wait_time = 60  # 最大等待60秒
        wait_time = 0
        
        while wait_time < max_wait_time:
            try:
                response = requests.get('http://localhost:5000/health', timeout=2)
                if response.status_code == 200:
                    print("✅ API服务启动成功!")
                    return process
            except:
                pass
            
            time.sleep(2)
            wait_time += 2
            print(f"⏳ 等待中... ({wait_time}/{max_wait_time}秒)")
        
        print("❌ API服务启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"❌ 启动API服务失败: {str(e)}")
        return None

def open_demo_ui():
    """打开演示界面"""
    print("\n🌐 打开演示界面...")
    
    ui_file = Path('POC-UI.html').absolute()
    if not ui_file.exists():
        print(f"❌ 演示界面文件不存在: {ui_file}")
        return False
    
    try:
        webbrowser.open(f'file://{ui_file}')
        print("✅ 演示界面已打开")
        return True
    except Exception as e:
        print(f"❌ 打开演示界面失败: {str(e)}")
        return False

def show_demo_info():
    """显示演示信息"""
    print("\n" + "="*60)
    print("🎯 碰撞测试数据异常检测系统 - 演示就绪")
    print("="*60)
    print("📋 演示步骤:")
    print("1. 在打开的网页中检查服务状态（绿色圆点表示在线）")
    print("2. 点击'选择文件'按钮上传测试数据")
    print("3. 推荐测试文件:")
    print("   - data/customer/1/CHANNEL/1.001 (异常比例: 7.57%)")
    print("   - data/customer/2/CHANNEL/2.001 (异常比例: 0.0%)")
    print("   - data/customer/3/CHANNEL/3.001 (异常比例: 12.51%)")
    print("4. 点击'开始异常检测'按钮")
    print("5. 查看检测结果和可视化图表")
    print("\n🔗 服务地址:")
    print("   - API服务: http://localhost:5000")
    print("   - 健康检查: http://localhost:5000/health")
    print("\n⚠️  注意事项:")
    print("   - 保持此终端窗口打开（API服务运行中）")
    print("   - 按 Ctrl+C 可停止服务")
    print("   - 如遇问题请查看演示说明.md文件")
    print("="*60)

def main():
    """主函数"""
    print("🎬 碰撞测试数据异常检测系统 - 演示启动器")
    print("="*60)
    
    # 1. 检查依赖
    if not check_dependencies():
        return
    
    # 2. 检查模型文件
    if not check_model_files():
        return
    
    # 3. 检查测试数据
    if not check_test_data():
        return
    
    # 4. 启动API服务
    api_process = start_api_service()
    if not api_process:
        return
    
    # 5. 打开演示界面
    if not open_demo_ui():
        api_process.terminate()
        return
    
    # 6. 显示演示信息
    show_demo_info()
    
    # 7. 等待用户停止
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        api_process.terminate()
        print("✅ 服务已停止")

if __name__ == '__main__':
    main()
