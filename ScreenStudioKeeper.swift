import Cocoa
import Foundation

@main
class ScreenStudioKeeper: NSObject, NSApplicationDelegate {
    
    private var statusItem: NSStatusItem?
    private var timer: Timer?
    private let appName = "Screen Studio"
    private var isMonitoring = false
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        setupStatusBar()
        setupMenu()
    }
    
    private func setupStatusBar() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.variableLength)
        statusItem?.button?.title = "📹"
        statusItem?.button?.toolTip = "Screen Studio Keeper"
    }
    
    private func setupMenu() {
        let menu = NSMenu()
        
        let startItem = NSMenuItem(title: "开始监控", action: #selector(startMonitoring), keyEquivalent: "")
        startItem.target = self
        menu.addItem(startItem)
        
        let stopItem = NSMenuItem(title: "停止监控", action: #selector(stopMonitoring), keyEquivalent: "")
        stopItem.target = self
        menu.addItem(stopItem)
        
        menu.addItem(NSMenuItem.separator())
        
        let quitItem = NSMenuItem(title: "退出", action: #selector(quit), keyEquivalent: "q")
        quitItem.target = self
        menu.addItem(quitItem)
        
        statusItem?.menu = menu
    }
    
    @objc private func startMonitoring() {
        guard !isMonitoring else { return }
        
        isMonitoring = true
        statusItem?.button?.title = "🔴"
        
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.keepScreenStudioVisible()
        }
        
        print("开始监控 Screen Studio")
    }
    
    @objc private func stopMonitoring() {
        guard isMonitoring else { return }
        
        isMonitoring = false
        statusItem?.button?.title = "📹"
        timer?.invalidate()
        timer = nil
        
        print("停止监控 Screen Studio")
    }
    
    private func keepScreenStudioVisible() {
        guard isScreenStudioRunning() else {
            print("Screen Studio 未运行")
            return
        }
        
        let script = """
        tell application "\(appName)"
            activate
            set visible of every window to true
            if (count of windows) > 0 then
                set index of window 1 to 1
            end if
        end tell
        """
        
        executeAppleScript(script)
    }
    
    private func isScreenStudioRunning() -> Bool {
        let runningApps = NSWorkspace.shared.runningApplications
        return runningApps.contains { app in
            app.localizedName?.contains(appName) == true
        }
    }
    
    private func executeAppleScript(_ script: String) {
        let appleScript = NSAppleScript(source: script)
        var error: NSDictionary?
        appleScript?.executeAndReturnError(&error)
        
        if let error = error {
            print("AppleScript 执行错误: \(error)")
        }
    }
    
    @objc private func quit() {
        stopMonitoring()
        NSApplication.shared.terminate(self)
    }
}

// 如果要编译为命令行工具，使用以下代码：
/*
import Foundation

class CommandLineKeeper {
    private let appName = "Screen Studio"
    private var isRunning = true
    
    func run() {
        print("🎬 Screen Studio界面保持工具启动")
        print("按 Ctrl+C 停止监控")
        
        signal(SIGINT) { _ in
            print("\n🛑 监控已停止")
            exit(0)
        }
        
        while isRunning {
            if isScreenStudioRunning() {
                keepVisible()
                print("✅ 窗口已显示", terminator: "\r")
            } else {
                print("⚠️  Screen Studio 未运行", terminator: "\r")
            }
            
            Thread.sleep(forTimeInterval: 1.0)
        }
    }
    
    private func isScreenStudioRunning() -> Bool {
        let task = Process()
        task.launchPath = "/usr/bin/pgrep"
        task.arguments = ["-f", appName]
        
        let pipe = Pipe()
        task.standardOutput = pipe
        task.launch()
        task.waitUntilExit()
        
        return task.terminationStatus == 0
    }
    
    private func keepVisible() {
        let script = """
        tell application "\(appName)"
            activate
            set visible of every window to true
        end tell
        """
        
        let task = Process()
        task.launchPath = "/usr/bin/osascript"
        task.arguments = ["-e", script]
        task.launch()
    }
}

let keeper = CommandLineKeeper()
keeper.run()
*/
