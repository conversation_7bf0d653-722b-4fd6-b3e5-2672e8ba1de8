#!/usr/bin/env python3
"""
Screen Studio简单保持工具
不需要辅助功能权限的版本
"""

import subprocess
import time
import signal
import sys

class SimpleKeeper:
    def __init__(self):
        self.app_name = "Screen Studio"
        self.running = True
        
    def is_app_running(self):
        """检查应用是否运行"""
        try:
            result = subprocess.run(["pgrep", "-f", self.app_name], capture_output=True)
            return result.returncode == 0
        except:
            return False
    
    def activate_app(self):
        """激活应用（不需要特殊权限）"""
        try:
            # 方法1: AppleScript activate
            result = subprocess.run([
                "osascript", "-e",
                f'tell application "{self.app_name}" to activate'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                return True, "AppleScript"
            
            # 方法2: open命令
            result = subprocess.run([
                "open", "-a", self.app_name
            ], capture_output=True, text=True)
            
            return result.returncode == 0, "open命令"
            
        except Exception as e:
            return False, f"错误: {e}"
    
    def signal_handler(self, signum, frame):
        """信号处理"""
        print("\n🛑 停止监控...")
        self.running = False
        sys.exit(0)
    
    def run(self):
        """主运行循环"""
        print("🎬 Screen Studio 简单保持工具")
        print("=" * 50)
        print(f"📱 监控应用: {self.app_name}")
        print("🔧 使用基础激活方法（无需特殊权限）")
        print("💡 每2秒激活一次应用，防止自动隐藏")
        print("按 Ctrl+C 停止监控\n")
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
        success_count = 0
        total_count = 0
        
        while self.running:
            try:
                total_count += 1
                
                if not self.is_app_running():
                    print("⚠️  Screen Studio 未运行，等待启动...", end="\r")
                    time.sleep(3)
                    continue
                
                # 激活应用
                success, method = self.activate_app()
                if success:
                    success_count += 1
                    print(f"✅ 应用已激活 ({method}) - 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)", end="\r")
                else:
                    print(f"❌ 激活失败 ({method}) - 成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)", end="\r")
                
                # 等待2秒
                time.sleep(2)
                
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"\n运行错误: {e}")
                time.sleep(2)
        
        print(f"\n📊 最终统计: 成功 {success_count}/{total_count} 次 ({success_count/total_count*100:.1f}%)")

def main():
    """主函数"""
    print("🚀 启动Screen Studio保持工具...")
    
    keeper = SimpleKeeper()
    keeper.run()

if __name__ == "__main__":
    main()
