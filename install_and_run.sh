#!/bin/bash

# Screen Studio Keeper 安装和运行脚本
# 作者：AI Assistant

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
INSTALL_DIR="$HOME/.screenstudio_keeper"

echo "🎬 Screen Studio Keeper 安装程序"
echo "=================================="

# 创建安装目录
mkdir -p "$INSTALL_DIR"

# 复制文件
echo "📁 复制文件到 $INSTALL_DIR"
cp "$SCRIPT_DIR/screen_studio_keeper.py" "$INSTALL_DIR/"
cp "$SCRIPT_DIR/keep_screenstudio.sh" "$INSTALL_DIR/"
cp "$SCRIPT_DIR/keep_screenstudio_visible.applescript" "$INSTALL_DIR/"

# 设置执行权限
chmod +x "$INSTALL_DIR/screen_studio_keeper.py"
chmod +x "$INSTALL_DIR/keep_screenstudio.sh"

echo "✅ 安装完成！"
echo ""

# 显示使用方法
cat << 'EOF'
🚀 使用方法：

方法1 - Python脚本（推荐）：
python3 ~/.screenstudio_keeper/screen_studio_keeper.py

方法2 - Shell脚本：
~/.screenstudio_keeper/keep_screenstudio.sh

方法3 - AppleScript：
osascript ~/.screenstudio_keeper/keep_screenstudio_visible.applescript

📝 使用步骤：
1. 启动 Screen Studio
2. 运行上述任一脚本
3. 开始录制（界面将保持可见）
4. 按 Ctrl+C 停止监控

💡 提示：
- 建议在录制前启动监控脚本
- 脚本会持续运行直到手动停止
- 如果Screen Studio重启，脚本会自动检测并重新监控

EOF

# 询问是否立即运行
echo ""
read -p "是否立即运行Python版本？(y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 启动监控..."
    python3 "$INSTALL_DIR/screen_studio_keeper.py"
fi
