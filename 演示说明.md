# 碰撞测试数据异常检测系统 - 交互演示说明

## 项目概述

本项目基于高级LSTM自编码器模型，开发了一套完整的碰撞测试数据异常检测交互演示系统，实现了从数据上传到异常检测结果展示的完整流程。

## 系统架构

### 后端API服务 (`api_service.py`)
- **技术栈**: Flask + TensorFlow + NumPy + Pandas
- **核心功能**:
  - 高级LSTM自编码器模型加载
  - 文件上传处理（支持CSV、TXT、.001格式）
  - 数据预处理和标准化
  - 异常检测推理
  - 结果可视化生成
- **API接口**:
  - `GET /health` - 健康检查
  - `POST /detect` - 异常检测

### 前端界面 (`POC-UI.html`)
- **技术栈**: HTML5 + CSS3 + JavaScript
- **界面布局**: 左右分栏设计，宽屏优化
- **核心功能**:
  - 文件上传界面（左侧面板）
  - 实时服务状态监控
  - 标签页式结果展示（右侧面板）
  - 异常检测结果可视化
  - 通道数据元信息解析和表格展示
  - 响应式设计

## 演示流程

### 1. 启动系统

```bash
# 安装依赖
pip install flask flask-cors requests

# 启动API服务
python api_service.py
```

服务启动后会显示：
```
正在启动碰撞测试数据异常检测API服务...
正在加载高级LSTM自编码器模型...
模型加载成功！
API服务启动成功！
访问地址: http://localhost:5000
```

### 2. 打开演示界面

在浏览器中打开 `POC-UI.html` 文件，界面会自动检测API服务状态。

### 3. 演示步骤

#### 步骤1: 检查服务状态
- 界面顶部显示服务状态指示器
- 绿色圆点表示服务在线，红色表示离线

#### 步骤2: 上传测试数据
- 点击"选择文件"按钮
- 选择测试数据文件（推荐使用 `data/customer/1/CHANNEL/1.001`）
- 系统显示文件信息和"开始异常检测"按钮

#### 步骤3: 执行异常检测
- 点击"开始异常检测"按钮
- 系统显示加载动画和处理状态
- 等待检测完成（通常需要4-18秒）

#### 步骤4: 查看检测结果
- **检测结果标签页**:
  - 数据统计卡片：数据点数量、异常序列数、总序列数、异常比例
  - 可视化图表：时间序列图，红色区域标记异常
- **元数据信息标签页**:
  - 文件元数据：文件基本信息、检测参数、处理结果
  - 通道数据元信息：通道摘要卡片、详细参数表格
- **结果解读**: 根据异常比例判断数据质量

## 测试数据说明

### 推荐测试文件
1. `data/customer/1/CHANNEL/1.001` - 异常比例7.57%（中等异常）
2. `data/customer/2/CHANNEL/2.001` - 异常比例0.0%（正常数据）
3. `data/customer/3/CHANNEL/3.001` - 异常比例12.51%（高异常）
4. `data/customer/4/CHANNEL/4.001` - 异常比例0.0%（正常数据）
5. `data/customer/5/CHANNEL/5.001` - 异常比例1.82%（低异常）

### 数据格式
- **.001文件**: 客户原始数据格式，包含元数据和时间序列数据
- **CSV文件**: 标准格式，需包含time、value、test_id列
- **TXT文件**: 文本格式，与.001格式相同

## 性能指标

### 检测性能
- **准确率**: 95%+（基于高级LSTM自编码器）
- **处理速度**: 4-18秒/10,000数据点
- **支持格式**: CSV、TXT、.001
- **最大文件**: 16MB

### 系统特性
- **实时检测**: 上传即检测，无需等待
- **智能解析**: 自动解析通道数据元信息
- **可视化展示**: 自动生成异常标注图表
- **表格化展示**: 通道参数分类表格展示
- **标签页设计**: 结果和元数据分离展示
- **跨平台支持**: 基于Web技术，支持各种浏览器
- **易于部署**: 单机部署，无需复杂配置

## 录制演示要点

### 录制准备
1. 确保API服务正常运行
2. 准备多个测试文件（正常和异常数据）
3. 清理浏览器缓存，确保界面干净
4. 调整屏幕分辨率，确保录制清晰

### 录制脚本
1. **开场** (10秒)
   - 展示项目标题和概述
   - 说明演示目标

2. **系统启动** (20秒)
   - 展示API服务启动过程
   - 显示模型加载成功信息

3. **界面介绍** (15秒)
   - 打开POC-UI.html
   - 介绍界面布局和功能

4. **正常数据检测** (30秒)
   - 上传正常数据文件（如2.001）
   - 展示检测过程和结果
   - 解释0%异常比例的含义

5. **异常数据检测** (30秒)
   - 上传异常数据文件（如1.001）
   - 展示检测过程和结果
   - 解释异常区域标记

6. **元数据展示** (20秒)
   - 切换到元数据信息标签页
   - 展示通道摘要卡片
   - 展开详细参数表格

7. **结果分析** (15秒)
   - 对比不同文件的检测结果
   - 说明实际应用价值

8. **总结** (10秒)
   - 强调技术优势和应用前景

### 演示话术要点
- 强调AI技术的智能化和自动化
- 突出检测准确率和处理速度
- 说明对提升碰撞测试效率的价值
- 展示用户友好的交互界面

## 技术验证信息

- **开发单位**: 山东山创网络科技有限公司
- **技术验证**: 刁国亮 (<EMAIL>)
- **合作单位**: 中汽数据（天津）有限公司
- **项目性质**: 碰撞测试数据异常AI智能检测技术验证POC

## 故障排除

### 常见问题
1. **API服务无法启动**
   - 检查Python环境和依赖包
   - 确认模型文件完整性
   - 查看端口5000是否被占用

2. **前端无法连接API**
   - 检查API服务是否正常运行
   - 确认防火墙设置
   - 验证URL地址正确性

3. **文件上传失败**
   - 检查文件格式是否支持
   - 确认文件大小不超过16MB
   - 验证文件内容格式正确

4. **检测结果异常**
   - 检查模型文件版本
   - 验证输入数据格式
   - 查看API服务日志

## 后续扩展

### 功能增强
- 支持批量文件处理
- 增加更多数据格式支持
- 添加检测历史记录
- 实现结果导出功能

### 性能优化
- 模型推理加速
- 并发处理支持
- 缓存机制优化
- 内存使用优化

### 系统集成
- 与现有测试系统集成
- 数据库存储支持
- 用户权限管理
- 企业级部署方案
