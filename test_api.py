# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-07-08
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API服务测试脚本
测试异常检测API的功能和性能
"""

import requests
import json
import time
import os

API_BASE_URL = 'http://localhost:5000'

def test_health_check():
    """测试健康检查接口"""
    print("🔍 测试健康检查接口...")
    try:
        response = requests.get(f'{API_BASE_URL}/health')
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 健康检查成功: {result}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {str(e)}")
        return False

def test_file_upload_and_detection(file_path):
    """测试文件上传和异常检测"""
    print(f"🔍 测试文件上传和异常检测: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ 测试文件不存在: {file_path}")
        return False
    
    try:
        # 准备文件上传
        with open(file_path, 'rb') as f:
            files = {'file': f}
            
            # 记录开始时间
            start_time = time.time()
            
            # 发送请求
            response = requests.post(f'{API_BASE_URL}/detect', files=files)
            
            # 记录结束时间
            end_time = time.time()
            processing_time = end_time - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 异常检测成功!")
                    print(f"   📁 文件名: {result['filename']}")
                    print(f"   📊 数据点数: {result['data_points']:,}")
                    print(f"   🚨 异常序列数: {result['anomaly_count']}")
                    print(f"   📈 总序列数: {result['total_sequences']}")
                    print(f"   📉 异常比例: {result['anomaly_percentage']}%")
                    print(f"   ⏱️  处理时间: {processing_time:.2f}秒")
                    print(f"   🖼️  可视化: {'已生成' if result.get('visualization') else '未生成'}")
                    return True
                else:
                    print(f"❌ 检测失败: {result.get('error', '未知错误')}")
                    return False
            else:
                print(f"❌ 请求失败: {response.status_code}")
                try:
                    error_info = response.json()
                    print(f"   错误信息: {error_info.get('error', '未知错误')}")
                except:
                    print(f"   响应内容: {response.text}")
                return False
                
    except Exception as e:
        print(f"❌ 测试异常: {str(e)}")
        return False

def test_multiple_files():
    """测试多个文件"""
    print("\n🔍 开始批量测试...")
    
    # 测试文件列表
    test_files = [
        'data/customer/1/CHANNEL/1.001',
        'data/customer/2/CHANNEL/2.001',
        'data/customer/3/CHANNEL/3.001',
        'data/customer/4/CHANNEL/4.001',
        'data/customer/5/CHANNEL/5.001'
    ]
    
    success_count = 0
    total_count = len(test_files)
    
    for i, file_path in enumerate(test_files, 1):
        print(f"\n--- 测试 {i}/{total_count} ---")
        if test_file_upload_and_detection(file_path):
            success_count += 1
        time.sleep(1)  # 避免请求过于频繁
    
    print(f"\n📊 批量测试结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def main():
    """主测试函数"""
    print("🚀 开始API服务测试...")
    print("=" * 50)
    
    # 1. 测试健康检查
    if not test_health_check():
        print("❌ 健康检查失败，请确保API服务已启动")
        return
    
    print("\n" + "=" * 50)
    
    # 2. 测试单个文件
    test_file = 'data/customer/1/CHANNEL/1.001'
    if test_file_upload_and_detection(test_file):
        print("✅ 单文件测试成功")
    else:
        print("❌ 单文件测试失败")
        return
    
    print("\n" + "=" * 50)
    
    # 3. 测试多个文件
    if test_multiple_files():
        print("✅ 批量测试成功")
    else:
        print("❌ 批量测试失败")
    
    print("\n" + "=" * 50)
    print("🎉 API服务测试完成!")

if __name__ == '__main__':
    main()
