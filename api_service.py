# Copyright (c) 2025 Shandong ShanChuang Network Technology Co., Ltd.
# All rights reserved.
#
# Author: diaoguoliang
# Email: <EMAIL>
# Date: 2025-07-08
#
# This software is proprietary and confidential. It is licensed under a
# commercial license agreement and may not be used, copied, or distributed
# without the prior written consent of Shandong ShanChuang Network Technology Co., Ltd.

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
碰撞测试数据异常检测API服务
基于高级LSTM自编码器模型提供RESTful API服务
支持文件上传、异常检测、结果可视化等功能
"""

import os
import io
import base64
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import joblib
import tensorflow as tf
from flask import Flask, request, jsonify, render_template_string
from flask_cors import CORS
from werkzeug.utils import secure_filename
import tempfile
import traceback
from datetime import datetime

# 配置
app = Flask(__name__)
CORS(app)  # 允许跨域请求
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 设置目录
MODEL_DIR = 'models'
UPLOAD_FOLDER = 'uploads'
RESULTS_FOLDER = 'api_results'

# 创建必要的目录
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(RESULTS_FOLDER, exist_ok=True)

# 模型参数
SEQUENCE_LENGTH = 100
STRIDE = 10
ALLOWED_EXTENSIONS = {'csv', 'txt', '001'}

# 全局变量存储加载的模型
model = None
scaler = None
threshold = None

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def load_model():
    """加载高级LSTM自编码器模型"""
    global model, scaler, threshold
    
    try:
        print("正在加载高级LSTM自编码器模型...")
        
        # 模型文件路径
        model_file = os.path.join(MODEL_DIR, 'advanced_lstm_autoencoder.h5')
        threshold_file = os.path.join(MODEL_DIR, 'advanced_lstm_threshold.npy')
        scaler_file = os.path.join(MODEL_DIR, 'advanced_lstm_scaler.pkl')
        
        # 检查文件是否存在
        if not os.path.exists(model_file):
            raise FileNotFoundError(f"模型文件不存在: {model_file}")
        if not os.path.exists(threshold_file):
            raise FileNotFoundError(f"阈值文件不存在: {threshold_file}")
        if not os.path.exists(scaler_file):
            raise FileNotFoundError(f"标准化器文件不存在: {scaler_file}")
        
        # 加载模型
        model = tf.keras.models.load_model(model_file, compile=False)
        model.compile(optimizer='adam', loss='mse')
        
        # 加载阈值和标准化器
        threshold = np.load(threshold_file)
        scaler = joblib.load(scaler_file)
        
        print("模型加载成功！")
        return True
        
    except Exception as e:
        print(f"模型加载失败: {str(e)}")
        traceback.print_exc()
        return False

def read_data_file(file_path):
    """读取数据文件，支持CSV和.001格式"""
    try:
        if file_path.endswith('.csv'):
            # CSV格式
            data = pd.read_csv(file_path)
            if 'time' not in data.columns or 'value' not in data.columns:
                raise ValueError("CSV文件必须包含'time'和'value'列")
            if 'test_id' not in data.columns:
                data['test_id'] = os.path.basename(file_path)
            return data
            
        elif file_path.endswith('.001') or file_path.endswith('.txt'):
            # .001格式（客户数据格式）
            with open(file_path, 'r') as f:
                lines = f.readlines()
            
            # 提取元数据
            metadata = {}
            data_start_line = 0
            
            for i, line in enumerate(lines):
                if ':' in line:
                    key, value = line.strip().split(':', 1)
                    metadata[key.strip()] = value.strip()
                else:
                    data_start_line = i
                    break
            
            # 提取数据值
            values = []
            for i in range(data_start_line, len(lines)):
                line = lines[i].strip()
                if line:
                    try:
                        values.append(float(line))
                    except ValueError:
                        continue
            
            # 创建时间序列
            time_start = float(metadata.get('Time of first sample', '0'))
            num_samples = len(values)
            time_step = 0.0001  # 假设采样率为10kHz
            
            times = np.arange(time_start, time_start + num_samples * time_step, time_step)[:len(values)]
            
            # 创建DataFrame
            data = pd.DataFrame({
                'time': times,
                'value': values,
                'test_id': os.path.basename(file_path)
            })
            
            return data
            
        else:
            raise ValueError(f"不支持的文件格式: {file_path}")
            
    except Exception as e:
        raise Exception(f"读取文件失败: {str(e)}")

def preprocess_data_for_inference(data):
    """预处理数据用于推理"""
    try:
        # 标准化数据
        values_scaled = scaler.transform(data[['value']])
        data_copy = data.copy()
        data_copy['value_scaled'] = values_scaled.flatten()
        
        # 创建序列数据
        sequences = []
        sequence_indices = []
        
        # 按测试ID分组处理
        for test_id, group in data_copy.groupby('test_id'):
            values = group['value_scaled'].values
            
            # 使用滑动窗口创建序列
            for i in range(0, len(values) - SEQUENCE_LENGTH + 1, STRIDE):
                seq = values[i:i+SEQUENCE_LENGTH]
                sequences.append(seq)
                sequence_indices.append(i)
        
        # 转换为numpy数组
        X = np.array(sequences)
        X = X.reshape(X.shape[0], X.shape[1], 1)  # 重塑为(样本数, 时间步, 特征数)
        
        return X, sequence_indices
        
    except Exception as e:
        raise Exception(f"数据预处理失败: {str(e)}")

def detect_anomalies(data):
    """检测异常"""
    try:
        # 预处理数据
        X, sequence_indices = preprocess_data_for_inference(data)
        
        # 使用模型进行预测
        X_pred = model.predict(X, verbose=0)
        
        # 计算重构误差
        errors = np.mean(np.square(X - X_pred), axis=(1, 2))
        
        # 标记异常
        anomalies = pd.DataFrame({
            'start_idx': sequence_indices,
            'end_idx': [i + SEQUENCE_LENGTH - 1 for i in sequence_indices],
            'reconstruction_error': errors,
            'is_anomaly': errors > threshold
        })
        
        return anomalies
        
    except Exception as e:
        raise Exception(f"异常检测失败: {str(e)}")

def create_visualization(data, anomalies):
    """创建异常检测可视化图表"""
    try:
        plt.figure(figsize=(15, 8))
        
        # 绘制原始时间序列
        plt.plot(data['time'], data['value'], label='原始数据', linewidth=1, alpha=0.8)
        
        # 标记异常区域
        anomaly_sequences = anomalies[anomalies['is_anomaly']]
        for _, row in anomaly_sequences.iterrows():
            start_idx = int(row['start_idx'])
            end_idx = int(row['end_idx'])
            if start_idx < len(data) and end_idx < len(data):
                plt.axvspan(data['time'].iloc[start_idx], data['time'].iloc[end_idx],
                           alpha=0.3, color='red', label='异常区域' if _ == anomaly_sequences.index[0] else "")
        
        plt.title('碰撞测试数据异常检测结果', fontsize=16, fontweight='bold')
        plt.xlabel('时间 (s)', fontsize=12)
        plt.ylabel('加速度值', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        
        # 保存图表到内存
        img_buffer = io.BytesIO()
        plt.savefig(img_buffer, format='png', dpi=150, bbox_inches='tight')
        img_buffer.seek(0)
        
        # 转换为base64编码
        img_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        
        plt.close()
        
        return img_base64
        
    except Exception as e:
        print(f"可视化创建失败: {str(e)}")
        return None

@app.route('/')
def index():
    """主页"""
    return jsonify({
        'message': '碰撞测试数据异常检测API服务',
        'version': '1.0',
        'status': 'running',
        'model_loaded': model is not None
    })

@app.route('/health')
def health():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'model_loaded': model is not None,
        'timestamp': datetime.now().isoformat()
    })

@app.route('/detect', methods=['POST'])
def detect():
    """异常检测接口"""
    try:
        # 检查模型是否已加载
        if model is None:
            return jsonify({'error': '模型未加载，请重启服务'}), 500
        
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({'error': '未找到上传文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '未选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': f'不支持的文件格式，支持的格式: {ALLOWED_EXTENSIONS}'}), 400
        
        # 保存上传的文件
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"{timestamp}_{filename}"
        file_path = os.path.join(UPLOAD_FOLDER, filename)
        file.save(file_path)
        
        try:
            # 读取数据
            data = read_data_file(file_path)
            
            # 检测异常
            anomalies = detect_anomalies(data)
            
            # 统计结果
            anomaly_count = int(anomalies['is_anomaly'].sum())
            total_sequences = len(anomalies)
            anomaly_percentage = (anomaly_count / total_sequences) * 100 if total_sequences > 0 else 0
            
            # 创建可视化
            visualization = create_visualization(data, anomalies)
            
            # 准备响应数据
            response_data = {
                'success': True,
                'filename': file.filename,
                'data_points': len(data),
                'anomaly_count': anomaly_count,
                'total_sequences': total_sequences,
                'anomaly_percentage': round(anomaly_percentage, 2),
                'anomalies': anomalies.to_dict('records'),
                'visualization': visualization,
                'timestamp': datetime.now().isoformat()
            }
            
            return jsonify(response_data)
            
        finally:
            # 清理临时文件
            if os.path.exists(file_path):
                os.remove(file_path)
        
    except Exception as e:
        error_msg = f"处理请求时发生错误: {str(e)}"
        print(error_msg)
        traceback.print_exc()
        return jsonify({'error': error_msg}), 500

if __name__ == '__main__':
    print("正在启动碰撞测试数据异常检测API服务...")
    
    # 加载模型
    if not load_model():
        print("模型加载失败，服务无法启动")
        exit(1)
    
    print("API服务启动成功！")
    print("访问地址: http://localhost:5000")
    print("健康检查: http://localhost:5000/health")
    
    # 启动Flask应用
    app.run(host='0.0.0.0', port=5000, debug=True)
